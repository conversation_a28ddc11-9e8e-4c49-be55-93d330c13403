/**
 * Standalone script to test the interactive mentor registration
 * This can be used to verify the registration flow without running full tests
 */

import { chromium } from '@playwright/test';
import { InteractiveMentorRegistration } from './interactive-mentor-registration';

async function testRegistration() {
    console.log('🚀 Testing Interactive Mentor Registration');
    console.log('This script will test the registration flow independently');
    
    const browser = await chromium.launch();
    const context = await browser.newContext();
    const request = context.request;
    
    try {
        const registration = new InteractiveMentorRegistration(request);
        const result = await registration.registerMentorInteractively();
        
        console.log('\n✅ Registration Test Completed Successfully!');
        console.log('📧 Email:', result.email);
        console.log('🔑 Access Token:', result.accessToken ? 'Obtained' : 'Failed');
        console.log('🔄 Refresh Token:', result.refreshToken ? 'Obtained' : 'Failed');
        
        return result;
        
    } catch (error) {
        console.error('\n❌ Registration Test Failed:', error.message);
        throw error;
    } finally {
        await context.close();
        await browser.close();
    }
}

// Run the test if this file is executed directly
if (require.main === module) {
    testRegistration()
        .then(() => {
            console.log('\n🎉 Test completed successfully');
            process.exit(0);
        })
        .catch((error) => {
            console.error('\n💥 Test failed:', error.message);
            process.exit(1);
        });
}

export { testRegistration };
