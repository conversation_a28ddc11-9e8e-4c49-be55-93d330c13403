import { test, expect, APIRequestContext } from '@playwright/test';
import ApplicationRequestsAPI from '../../../api/application-requests/application-requests.api';
import { InteractiveMentorRegistration } from './interactive-mentor-registration';
import testData from '../../../tests-data/mentor-application-requests-api-data.json';
import { 
    MentorApplicationRequestTestData, 
    InteractiveRegistrationResult,
    ApplicationRequestCreateData,
    ApplicationRequestResponse 
} from '../../../data-type/mentor-application-request-api.type';

test.describe('Mentor Application Requests API Tests', () => {
    let applicationRequestsAPI: ApplicationRequestsAPI;
    let mentorCredentials: InteractiveRegistrationResult;
    let authenticatedRequest: APIRequestContext;
    let createdApplicationId: string;
    
    const data = testData as MentorApplicationRequestTestData;

    test.beforeAll(async ({ playwright }) => {
        console.log('\n🚀 Starting Mentor Application Request API Tests');
        console.log('This will require interactive input for mentor registration');
        
        // Create a temporary request context for registration
        const tempRequest = await playwright.request.newContext();
        
        try {
            // Interactive mentor registration
            const registration = new InteractiveMentorRegistration(tempRequest);
            mentorCredentials = await registration.registerMentorInteractively();
            
            // Create authenticated request context
            authenticatedRequest = await playwright.request.newContext({
                extraHTTPHeaders: {
                    Authorization: `Bearer ${mentorCredentials.accessToken}`,
                },
            });
            
            console.log('\n✅ Mentor registration completed successfully');
            console.log('📧 Email:', mentorCredentials.email);
            console.log('🔑 Access token obtained');
            
        } catch (error) {
            console.error('\n❌ Failed to register mentor:', error.message);
            throw error;
        } finally {
            await tempRequest.dispose();
        }
    });

    test.beforeEach(async () => {
        applicationRequestsAPI = new ApplicationRequestsAPI(authenticatedRequest);
    });

    test.afterAll(async () => {
        if (authenticatedRequest) {
            await authenticatedRequest.dispose();
        }
    });

    test('MAR_001 - Create application request successfully', async () => {
        let response: any;
        let responseBody: any;

        await test.step('Send POST request to create application request', async () => {
            const createData = data.applicationRequestData.createRequest.validData;
            response = await applicationRequestsAPI.createApplicationRequest(createData);
            responseBody = await response.text();
        });

        await test.step('Verify successful creation', async () => {
            expect(response.status()).toBe(200);
            expect(responseBody).toBe(data.expectedResponses.createSuccess);
        });

        await test.step('Get current user application request to verify creation', async () => {
            const getCurrentResponse = await applicationRequestsAPI.getCurrentUserApplicationRequest();
            expect(getCurrentResponse.status()).toBe(200);
            
            const currentAppBody = await getCurrentResponse.json();
            expect(currentAppBody).toBeDefined();
            expect(currentAppBody.education).toBe(data.applicationRequestData.createRequest.validData.Education);
            expect(currentAppBody.workExperience).toBe(data.applicationRequestData.createRequest.validData.WorkExperience);
            expect(currentAppBody.description).toBe(data.applicationRequestData.createRequest.validData.Description);
            expect(currentAppBody.status).toBe(data.applicationStatuses.pending);
            
            // Store the created application ID for subsequent tests
            createdApplicationId = currentAppBody.id;
        });
    });

    test('MAR_002 - Get current user application request', async () => {
        let response: any;
        let responseBody: any;

        await test.step('Send GET request for current user application', async () => {
            response = await applicationRequestsAPI.getCurrentUserApplicationRequest();
            responseBody = await response.json();
        });

        await test.step('Verify response structure and data', async () => {
            expect(response.status()).toBe(200);
            expect(responseBody).toBeDefined();
            expect(responseBody.id).toBeDefined();
            expect(responseBody.education).toBeDefined();
            expect(responseBody.workExperience).toBeDefined();
            expect(responseBody.description).toBeDefined();
            expect(responseBody.status).toBeDefined();
            expect(responseBody.fullName).toBe(data.mentorRegistrationData.fullname);
        });
    });

    test('MAR_003 - Update application request successfully', async () => {
        let response: any;
        let responseBody: any;

        await test.step('Prepare update data with application ID', async () => {
            expect(createdApplicationId).toBeDefined();
        });

        await test.step('Send PUT request to update application request', async () => {
            const updateData = {
                Id: createdApplicationId,
                ...data.applicationRequestData.updateRequest.validData
            };
            response = await applicationRequestsAPI.updateApplicationRequest(updateData);
            responseBody = await response.text();
        });

        await test.step('Verify successful update', async () => {
            expect(response.status()).toBe(200);
            expect(responseBody).toBe(data.expectedResponses.updateSuccess);
        });

        await test.step('Verify updated data', async () => {
            const getCurrentResponse = await applicationRequestsAPI.getCurrentUserApplicationRequest();
            const currentAppBody = await getCurrentResponse.json();

            expect(currentAppBody.education).toBe(data.applicationRequestData.updateRequest.validData.Education);
            expect(currentAppBody.workExperience).toBe(data.applicationRequestData.updateRequest.validData.WorkExperience);
            expect(currentAppBody.description).toBe(data.applicationRequestData.updateRequest.validData.Description);
        });
    });

    test('MAR_004 - Get application request by ID', async () => {
        let response: any;
        let responseBody: any;

        await test.step('Send GET request for application by ID', async () => {
            expect(createdApplicationId).toBeDefined();
            response = await applicationRequestsAPI.getApplicationRequestById(createdApplicationId);
            responseBody = await response.json();
        });

        await test.step('Verify response structure and data', async () => {
            expect(response.status()).toBe(200);
            expect(responseBody).toBeDefined();
            expect(responseBody.id).toBe(createdApplicationId);
            expect(responseBody.mentorEmail).toBe(mentorCredentials.email);
            expect(responseBody.fullName).toBe(data.mentorRegistrationData.fullname);
            expect(responseBody.education).toBeDefined();
            expect(responseBody.workExperience).toBeDefined();
            expect(responseBody.description).toBeDefined();
            expect(responseBody.status).toBeDefined();
            expect(responseBody.applicationRequestDocuments).toBeDefined();
        });
    });

    test('MAR_005 - Get application requests with pagination', async () => {
        let response: any;
        let responseBody: any;

        await test.step('Send GET request with pagination parameters', async () => {
            const params = data.applicationRequestData.getRequests.validParams;
            response = await applicationRequestsAPI.getApplicationRequests(params);
            responseBody = await response.json();
        });

        await test.step('Verify pagination response structure', async () => {
            expect(response.status()).toBe(200);
            expect(responseBody).toBeDefined();
            expect(Array.isArray(responseBody.data)).toBeTruthy();
            expect(responseBody.totalCount).toBeDefined();
            expect(responseBody.pageNumber).toBe(data.applicationRequestData.getRequests.validParams.PageNumber);
            expect(responseBody.pageSize).toBe(data.applicationRequestData.getRequests.validParams.PageSize);
        });

        await test.step('Verify application data in list', async () => {
            const applications = responseBody.data as ApplicationRequestResponse[];
            const currentApplication = applications.find(app => app.id === createdApplicationId);

            if (currentApplication) {
                expect(currentApplication.fullName).toBe(data.mentorRegistrationData.fullname);
                expect(currentApplication.status).toBeDefined();
            }
        });
    });

    test('MAR_006 - Get application requests with search', async () => {
        let response: any;
        let responseBody: any;

        await test.step('Send GET request with search parameters', async () => {
            const searchParams = {
                ...data.applicationRequestData.getRequests.searchParams,
                Search: data.mentorRegistrationData.fullname
            };
            response = await applicationRequestsAPI.getApplicationRequests(searchParams);
            responseBody = await response.json();
        });

        await test.step('Verify search results', async () => {
            expect(response.status()).toBe(200);
            expect(responseBody).toBeDefined();
            expect(Array.isArray(responseBody.data)).toBeTruthy();

            const applications = responseBody.data as ApplicationRequestResponse[];
            const foundApplication = applications.find(app => app.fullName.includes(data.mentorRegistrationData.fullname));
            expect(foundApplication).toBeDefined();
        });
    });

    test('MAR_007 - Create application request without certifications', async () => {
        // First, we need to create a new mentor account for this test
        // This test demonstrates creating an application without certifications
        let response: any;
        let responseBody: any;

        await test.step('Send POST request without certifications', async () => {
            const createData = data.applicationRequestData.createRequest.validDataWithoutCertifications;
            response = await applicationRequestsAPI.createApplicationRequest(createData);
            responseBody = await response.text();
        });

        await test.step('Verify creation fails due to existing application', async () => {
            // Since we already have an application, this should fail
            expect(response.status()).toBe(400);
        });
    });

    test('MAR_008 - Update non-existent application request', async () => {
        let response: any;
        let responseBody: any;

        await test.step('Send PUT request with non-existent ID', async () => {
            const updateData = {
                Id: "00000000-0000-0000-0000-000000000000",
                ...data.applicationRequestData.updateRequest.validData
            };
            response = await applicationRequestsAPI.updateApplicationRequest(updateData);
            responseBody = await response.text();
        });

        await test.step('Verify application not found error', async () => {
            expect(response.status()).toBe(404);
            expect(responseBody).toContain(data.expectedResponses.applicationNotFound);
        });
    });

    test('MAR_009 - Get non-existent application request by ID', async () => {
        let response: any;
        let responseBody: any;

        await test.step('Send GET request with non-existent ID', async () => {
            response = await applicationRequestsAPI.getApplicationRequestById("00000000-0000-0000-0000-000000000000");
            responseBody = await response.text();
        });

        await test.step('Verify application not found error', async () => {
            expect(response.status()).toBe(404);
            expect(responseBody).toContain(data.expectedResponses.applicationNotFound);
        });
    });

    test('MAR_010 - Verify application request status flow', async () => {
        let getCurrentResponse: any;
        let currentAppBody: any;

        await test.step('Verify initial status is pending', async () => {
            getCurrentResponse = await applicationRequestsAPI.getCurrentUserApplicationRequest();
            currentAppBody = await getCurrentResponse.json();

            expect(currentAppBody.status).toBe(data.applicationStatuses.pending);
        });

        await test.step('Verify application has required fields', async () => {
            expect(currentAppBody.id).toBeDefined();
            expect(currentAppBody.education).toBeDefined();
            expect(currentAppBody.workExperience).toBeDefined();
            expect(currentAppBody.description).toBeDefined();
            expect(currentAppBody.fullName).toBe(data.mentorRegistrationData.fullname);
            expect(currentAppBody.summitted).toBeDefined();
        });
    });
});
