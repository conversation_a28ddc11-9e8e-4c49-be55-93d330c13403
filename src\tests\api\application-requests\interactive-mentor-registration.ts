import { APIRequestContext } from '@playwright/test';
import RegisterAP<PERSON> from '../../../api/register/register.api';
import VerifyEmailAPI from '../../../api/verify-email/verify-email.api';
import { mapToRegisterRequest } from '../../../data-type/register-user-mapper.type';
import testData from '../../../tests-data/mentor-application-requests-api-data.json';
import { MentorRegistrationData, InteractiveRegistrationResult } from '../../../data-type/mentor-application-request-api.type';
import * as readline from 'readline';

export class InteractiveMentorRegistration {
    private request: APIRequestContext;
    private registerAPI: RegisterAPI;
    private verifyEmailAPI: VerifyEmailAPI;
    private rl: readline.Interface;

    constructor(request: APIRequestContext) {
        this.request = request;
        this.registerAPI = new RegisterAPI(request);
        this.verifyEmailAPI = new VerifyEmailAPI(request);
        this.rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout
        });
    }

    private async promptUser(question: string): Promise<string> {
        return new Promise((resolve) => {
            this.rl.question(question, (answer) => {
                resolve(answer.trim());
            });
        });
    }

    private async promptPassword(question: string): Promise<string> {
        return new Promise((resolve) => {
            process.stdout.write(question);
            process.stdin.setRawMode(true);
            process.stdin.resume();
            process.stdin.setEncoding('utf8');
            
            let password = '';
            const onData = (char: string) => {
                switch (char) {
                    case '\n':
                    case '\r':
                    case '\u0004': // Ctrl+D
                        process.stdin.setRawMode(false);
                        process.stdin.pause();
                        process.stdin.removeListener('data', onData);
                        console.log('');
                        resolve(password);
                        break;
                    case '\u0003': // Ctrl+C
                        process.exit();
                        break;
                    case '\u007f': // Backspace
                        if (password.length > 0) {
                            password = password.slice(0, -1);
                            process.stdout.write('\b \b');
                        }
                        break;
                    default:
                        password += char;
                        process.stdout.write('*');
                        break;
                }
            };
            
            process.stdin.on('data', onData);
        });
    }

    async registerMentorInteractively(): Promise<InteractiveRegistrationResult> {
        try {
            console.log('\n=== Interactive Mentor Registration for API Testing ===');
            console.log('Please use a real email from 10min email service for testing');
            console.log('You will receive a verification code that you need to enter');
            
            // Step 1: Get email and password from user
            const email = await this.promptUser('\nEnter your email address (from 10min email): ');
            const password = await this.promptPassword('Enter your password (will be hidden): ');
            
            if (!email || !password) {
                throw new Error('Email and password are required');
            }

            console.log('\n📧 Email:', email);
            console.log('🔒 Password: [HIDDEN]');
            
            // Step 2: Prepare registration data
            const registrationData: MentorRegistrationData = {
                ...testData.mentorRegistrationData,
                email: email,
                password: password
            };

            console.log('\n⏳ Registering mentor account...');
            
            // Step 3: Register the mentor
            const mappedData = mapToRegisterRequest(registrationData);
            const registerResponse = await this.registerAPI.register(mappedData);
            
            if (!registerResponse.ok()) {
                const errorText = await registerResponse.text();
                throw new Error(`Registration failed: ${errorText}`);
            }

            console.log('✅ Registration successful! Check your email for verification code.');
            
            // Step 4: Get verification code from user
            const verificationCode = await this.promptUser('\nEnter the 6-digit verification code from your email: ');
            
            if (!verificationCode || verificationCode.length !== 6) {
                throw new Error('Verification code must be exactly 6 digits');
            }

            console.log('\n⏳ Verifying email...');
            
            // Step 5: Verify email
            const verifyResponse = await this.verifyEmailAPI.verifyEmail({
                email: email,
                code: verificationCode
            });

            if (!verifyResponse.ok()) {
                const errorText = await verifyResponse.text();
                throw new Error(`Email verification failed: ${errorText}`);
            }

            const verifyResponseBody = await verifyResponse.json();
            
            console.log('✅ Email verification successful!');
            console.log('🎉 Mentor account is ready for API testing');
            
            return {
                email: email,
                password: password,
                accessToken: verifyResponseBody.accessToken,
                refreshToken: verifyResponseBody.refreshToken
            };

        } catch (error) {
            console.error('\n❌ Registration failed:', error.message);
            throw error;
        } finally {
            this.rl.close();
        }
    }

    async cleanup(): Promise<void> {
        this.rl.close();
    }
}
