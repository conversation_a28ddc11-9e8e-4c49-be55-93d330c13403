# Mentor Application Requests API Tests

This directory contains API tests for mentor application requests with interactive mentor registration.

## Overview

The tests follow the Page Object Model (POM) pattern and include:
- Interactive mentor registration using real email from 10min email service
- Comprehensive API testing for all application request endpoints
- Test data management using separate JSON files
- Data type definitions for type safety

## Files Structure

```
src/tests/api/application-requests/
├── mentor-application-requests.spec.ts    # Main API test file
├── interactive-mentor-registration.ts     # Interactive registration script
└── README.md                             # This file

src/tests-data/
└── mentor-application-requests-api-data.json  # Test data for API tests

src/data-type/
└── mentor-application-request-api.type.ts     # TypeScript type definitions
```

## Prerequisites

1. **10min Email Service**: Go to https://10minutemail.com/ to get a temporary email
2. **Node.js and npm**: Ensure you have Node.js installed
3. **Playwright**: Make sure <PERSON><PERSON> is properly configured

## How to Run the Tests

### Step 1: Prepare Email
1. Go to https://10minutemail.com/
2. Copy the generated email address
3. Keep the tab open to receive verification codes

### Step 2: Run the Tests
```bash
# Run all mentor application request tests
npx playwright test src/tests/api/application-requests/mentor-application-requests.spec.ts

# Run with UI mode for better debugging
npx playwright test src/tests/api/application-requests/mentor-application-requests.spec.ts --ui

# Run specific test
npx playwright test src/tests/api/application-requests/mentor-application-requests.spec.ts -g "MAR_001"
```

### Step 3: Interactive Input
When the tests start, you'll be prompted to:
1. **Enter email**: Paste the email from 10min email service
2. **Enter password**: Type a secure password (will be hidden)
3. **Enter verification code**: Check your email and enter the 6-digit code

## Test Cases Covered

| Test ID | Description | API Endpoint |
|---------|-------------|--------------|
| MAR_001 | Create application request successfully | POST /api/application-requests |
| MAR_002 | Get current user application request | GET /api/application-requests/current-user |
| MAR_003 | Update application request successfully | PUT /api/application-requests |
| MAR_004 | Get application request by ID | GET /api/application-requests/{id} |
| MAR_005 | Get application requests with pagination | GET /api/application-requests |
| MAR_006 | Get application requests with search | GET /api/application-requests |
| MAR_007 | Create application request without certifications | POST /api/application-requests |
| MAR_008 | Update non-existent application request | PUT /api/application-requests |
| MAR_009 | Get non-existent application request by ID | GET /api/application-requests/{id} |
| MAR_010 | Verify application request status flow | GET /api/application-requests/current-user |

## Test Data

The test data is stored in `src/tests-data/mentor-application-requests-api-data.json` and includes:
- Mentor registration data with all required fields
- Application request data for create/update operations
- Expected responses and error messages
- Application status constants

## Features

### Interactive Registration
- **Real Email Integration**: Uses actual email service for realistic testing
- **Secure Password Input**: Password is hidden during input
- **Email Verification**: Handles the complete registration flow with email verification
- **Error Handling**: Comprehensive error handling for registration failures

### Comprehensive API Testing
- **CRUD Operations**: Create, Read, Update operations for application requests
- **Pagination Testing**: Tests pagination and search functionality
- **Error Scenarios**: Tests error cases like non-existent resources
- **Status Flow Verification**: Verifies application status transitions

### Type Safety
- **TypeScript Types**: Full type definitions for all data structures
- **Data Validation**: Runtime validation of API responses
- **Intellisense Support**: Full IDE support with type hints

## Configuration

The tests use the following configuration:
- **Base URL**: Configured through Playwright config
- **Authentication**: Bearer token authentication after registration
- **Timeouts**: Standard Playwright timeouts
- **Retry Logic**: Built-in retry for flaky network operations

## Troubleshooting

### Common Issues

1. **Email Verification Timeout**
   - Make sure to check your email quickly
   - The verification code expires after a few minutes
   - Refresh the 10min email page if needed

2. **Registration Failures**
   - Ensure the email format is valid
   - Check that the password meets requirements
   - Verify network connectivity

3. **Test Failures**
   - Check if the API endpoints are accessible
   - Verify authentication tokens are valid
   - Review test data for any invalid values

### Debug Mode
Run tests with debug flags for more information:
```bash
npx playwright test src/tests/api/application-requests/mentor-application-requests.spec.ts --debug
```

## Notes

- Each test run creates a new mentor account
- The interactive registration only runs once per test session
- Tests are designed to run independently but share the same mentor account
- Clean up is handled automatically after test completion
