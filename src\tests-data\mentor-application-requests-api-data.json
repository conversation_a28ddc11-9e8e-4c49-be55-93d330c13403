{"mentorRegistrationData": {"avatarUrl": "", "fullname": "Test Mentor API", "role": "2", "bio": "Experienced mentor for API testing", "IsNotification": true, "IsReceiveMessage": true, "IsPrivateProfile": false, "expertises": "8a5bc300-21c4-47d0-bb33-27d0a709d417", "professionalSkill": "Software Development and Mentoring", "experience": "5+ years in software development and mentoring", "communicationPreference": 1, "goals": "Help developers grow their skills through mentoring", "courseCategoryIds": "f47ac10b-58cc-4372-a567-0e02b2c3d479", "sessionFrequency": 2, "duration": 60, "learningStyle": 1, "teachingStyles": 1, "Availability": [1, 2, 3]}, "applicationRequestData": {"createRequest": {"validData": {"Education": "Master's degree in Computer Science from ABC University", "WorkExperience": "5 years as Senior Software Developer at XYZ Company, 2 years as Team Lead", "Certifications": ["AWS Certified Solutions Architect", "Scrum Master Certification"], "Description": "Passionate about mentoring junior developers and sharing knowledge in software development best practices", "Status": 1}, "validDataWithoutCertifications": {"Education": "Bachelor's degree in Software Engineering", "WorkExperience": "3 years as Full Stack Developer", "Certifications": [], "Description": "Eager to help others learn programming fundamentals"}}, "updateRequest": {"validData": {"Education": "Updated: PhD in Computer Science with specialization in AI", "WorkExperience": "Updated: 7 years as Senior Software Developer, 3 years as Technical Lead", "Certifications": ["AWS Certified Solutions Architect", "Google Cloud Professional", "Azure Fundamentals"], "Description": "Updated: Experienced mentor with expertise in AI and machine learning"}}, "getRequests": {"validParams": {"PageSize": 10, "PageNumber": 1, "ApplicationRequestStatuses": [1, 2, 3]}, "searchParams": {"PageSize": 5, "PageNumber": 1, "Search": "Test Mentor", "ApplicationRequestStatuses": [1]}}, "requestUpdate": {"validData": {"note": "Please provide more details about your teaching methodology and experience with remote mentoring"}}, "approveRequest": {"expectedMessage": "Request approved successfully"}, "rejectRequest": {"validData": {"note": "Application does not meet our current requirements. Please reapply with more relevant experience."}}}, "expectedResponses": {"createSuccess": "Create successfully", "updateSuccess": "Update successfully", "applicationNotFound": "Application request not found", "cannotUpdateNonReview": "<PERSON><PERSON> cannot update request that is not under review", "cannotRequestUpdateReview": "Admin cannot request update request that is under review", "cannotApproveReview": "Admin cannot approve request that is under review", "cannotRejectReview": "Admin cannot reject request that is under review", "cannotRejectApproved": "Cannot reject approved request"}, "applicationStatuses": {"pending": 1, "underReview": 2, "approved": 3, "rejected": 4, "requestUpdate": 5}}