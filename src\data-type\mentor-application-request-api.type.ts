export interface MentorRegistrationData {
    email: string;
    password: string;
    avatarUrl: string;
    fullname: string;
    role: string;
    bio: string;
    IsNotification: boolean;
    IsReceiveMessage: boolean;
    IsPrivateProfile: boolean;
    expertises: string;
    professionalSkill: string;
    experience: string;
    communicationPreference: number;
    goals: string;
    courseCategoryIds: string;
    sessionFrequency: number;
    duration: number;
    learningStyle: number;
    teachingStyles: number;
    Availability: number[];
}

export interface ApplicationRequestCreateData {
    Education: string;
    WorkExperience: string;
    Certifications: string[];
    Description: string;
    Status?: number;
}

export interface ApplicationRequestUpdateData {
    Id: string;
    Education: string;
    WorkExperience: string;
    Certifications: string[];
    Description: string;
}

export interface GetApplicationRequestsParams {
    PageSize: number;
    PageNumber: number;
    Search?: string;
    ApplicationRequestStatuses?: number[];
}

export interface RequestUpdateData {
    note: string;
}

export interface RejectRequestData {
    note: string;
}

export interface ApplicationRequestResponse {
    id: string;
    education: string;
    workExperience: string;
    fullName: string;
    description: string;
    status: number;
    summitted: string;
}

export interface ApplicationRequestDetailResponse extends ApplicationRequestResponse {
    note: string;
    applicationRequestDocuments: ApplicationDocument[];
    mentorEmail: string;
    mentorExpertises: number[];
    mentorCertifications: string;
    avatarUrl: string;
}

export interface ApplicationDocument {
    filePath: string;
    fileName: string;
}

export interface MentorApplicationRequestTestData {
    mentorRegistrationData: MentorRegistrationData;
    applicationRequestData: {
        createRequest: {
            validData: ApplicationRequestCreateData;
            validDataWithoutCertifications: ApplicationRequestCreateData;
        };
        updateRequest: {
            validData: Omit<ApplicationRequestUpdateData, 'Id'>;
        };
        getRequests: {
            validParams: GetApplicationRequestsParams;
            searchParams: GetApplicationRequestsParams;
        };
        requestUpdate: {
            validData: RequestUpdateData;
        };
        approveRequest: {
            expectedMessage: string;
        };
        rejectRequest: {
            validData: RejectRequestData;
        };
    };
    expectedResponses: {
        createSuccess: string;
        updateSuccess: string;
        applicationNotFound: string;
        cannotUpdateNonReview: string;
        cannotRequestUpdateReview: string;
        cannotApproveReview: string;
        cannotRejectReview: string;
        cannotRejectApproved: string;
    };
    applicationStatuses: {
        pending: number;
        underReview: number;
        approved: number;
        rejected: number;
        requestUpdate: number;
    };
}

export interface InteractiveRegistrationResult {
    email: string;
    password: string;
    accessToken: string;
    refreshToken: string;
}
